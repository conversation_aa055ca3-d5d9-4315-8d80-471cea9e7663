using LeadHistoryMigration.Application.ExistingHistory;
using LeadHistoryMigration.Application.NewLeadHistory;

namespace LeadHistoryMigration.Infra.NewLeadHistory
{
    public class LeadHistoryTransformer : ILeadHistoryTransformer
    {
        public List<LeadHistoryHot> TransformToFieldWise(LeadHistory leadHistory)
        {
            var fieldWiseRecords = new List<LeadHistoryHot>();

            // Get the modified dates and last modified by users for each version
            var modifiedDates = leadHistory.ModifiedDate ?? new Dictionary<int, DateTime>();
            var lastModifiedByUsers = leadHistory.LastModifiedByUser ?? new Dictionary<int, Guid>();
            var assignedToUsers = leadHistory.AssignedToUser ?? new Dictionary<int, string>();

            // Process each JSONB field
            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Name", "String",
                leadHistory.Name, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Contact No", "String",
                leadHistory.ContactNo, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Email", "String",
                leadHistory.Email, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Alternate Contact No", "String",
                leadHistory.AlternateContactNo, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Lead Number", "String",
                leadHistory.LeadNumber, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Notes", "String",
                leadHistory.Notes, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Confidential Notes", "String",
                leadHistory.ConfidentialNotes, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Lower Budget", "Int64",
                leadHistory.LowerBudget, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Upper Budget", "Int64",
                leadHistory.UpperBudget, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Area", "Double",
                leadHistory.Area, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Area Unit", "String",
                leadHistory.AreaUnit, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Currency", "String",
                leadHistory.Currency, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Base Lead Status", "String",
                leadHistory.BaseLeadStatus, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Sub Lead Status", "String",
                leadHistory.SubLeadStatus, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Rating", "String",
                leadHistory.Rating, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Scheduled Date", "Nullable`1",
                leadHistory.ScheduledDate, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Picked Date", "Nullable`1",
                leadHistory.PickedDate, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Is Picked", "Boolean",
                leadHistory.IsPicked, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Assign To", "Guid",
                leadHistory.AssignedTo, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Assigned User", "UserDto",
                leadHistory.AssignedToUser, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Assigned From User", "UserDto",
                leadHistory.AssignedFromUser, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Customer City", "String",
                leadHistory.CustomerCity, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Customer State", "String",
                leadHistory.CustomerState, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Customer Location", "String",
                leadHistory.CustomerLocation, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Customer Country", "String",
                leadHistory.CustomerCountry, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Enquired City", "String",
                leadHistory.EnquiredCity, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Enquired State", "String",
                leadHistory.EnquiredState, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Enquired Location", "String",
                leadHistory.EnquiredLocation, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Enquired Country", "String",
                leadHistory.EnquiredCountry, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Lead Source", "LeadSource",
                leadHistory.LeadSource, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Enquired For", "EnquiryType",
                leadHistory.EnquiredFor, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Sale Type", "SaleType",
                leadHistory.SaleType, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Is Hot Lead", "Boolean",
                leadHistory.IsHotLead, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Is Cold Lead", "Boolean",
                leadHistory.IsColdLead, modifiedDates, lastModifiedByUsers, assignedToUsers);

            ProcessDictionaryField(fieldWiseRecords, leadHistory, "Is Warm Lead", "Boolean",
                leadHistory.IsWarmLead, modifiedDates, lastModifiedByUsers, assignedToUsers);

            // Add more fields as needed...

            return fieldWiseRecords;
        }

        public async Task<List<LeadHistoryHot>> TransformMultipleToFieldWiseAsync(List<LeadHistory> leadHistories)
        {
            return await Task.Run(() =>
            {
                var allFieldWiseRecords = new List<LeadHistoryHot>();

                foreach (var leadHistory in leadHistories)
                {
                    allFieldWiseRecords.AddRange(TransformToFieldWise(leadHistory));
                }

                return allFieldWiseRecords;
            });
        }

        private void ProcessDictionaryField<T>(
            List<LeadHistoryHot> records,
            LeadHistory leadHistory,
            string fieldName,
            string fieldType,
            IDictionary<int, T>? fieldData,
            IDictionary<int, DateTime> modifiedDates,
            IDictionary<int, Guid> lastModifiedByUsers,
            IDictionary<int, string> assignedToUsers)
        {
            if (fieldData == null || !fieldData.Any()) return;

            // Sort by version to get proper old/new value pairs
            var sortedVersions = fieldData.OrderBy(x => x.Key).ToList();

            for (int i = 0; i < sortedVersions.Count; i++)
            {
                var currentVersion = sortedVersions[i].Key;
                var currentValue = sortedVersions[i].Value;

                // Get old value (from previous version if exists)
                string? oldValue = i > 0
                    ? ConvertToString(sortedVersions[i - 1].Value)
                    : null;

                string? newValue = ConvertToString(currentValue);

                // Only create record if there's a change
                if (oldValue != newValue)
                {
                    var modifiedDate = modifiedDates.ContainsKey(currentVersion)
                        ? modifiedDates[currentVersion]
                        : leadHistory.CreatedDate;

                    var lastModifiedBy = lastModifiedByUsers.ContainsKey(currentVersion)
                        ? lastModifiedByUsers[currentVersion]
                        : leadHistory.CreatedBy;

                    var modifiedByUserName = assignedToUsers.ContainsKey(currentVersion)
                        ? assignedToUsers[currentVersion]
                        : string.Empty;

                    records.Add(new LeadHistoryHot
                    {
                        Id = Guid.NewGuid(),
                        LeadId = leadHistory.LeadId,
                        FieldName = fieldName,
                        FieldType = fieldType,
                        OldValue = oldValue,
                        NewValue = newValue,
                        ModifiedBy = modifiedByUserName,
                        ModifiedOn = modifiedDate,
                        LastModifiedById = lastModifiedBy,
                        Version = currentVersion,
                        TenantId = "ratify", // From your sample data
                        IsDeleted = leadHistory.IsDeleted
                    });
                }
            }
        }

        private string? ConvertToString<T>(T? value)
        {
            if (value == null) return null;

            if (value is DateTime dateTime)
                return dateTime.ToString("MM/dd/yyyy HH:mm:ss");

            if (value is bool boolValue)
                return boolValue ? "True" : "False";

            if (value is Guid guidValue)
                return guidValue.ToString();

            return value.ToString();
        }
    }
}
